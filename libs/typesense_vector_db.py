# ============================================================================
# TYPESENSE VECTOR DATABASE LIBRARY
# ============================================================================

import os
import json
import uuid
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime
import typesense
from dotenv import load_dotenv
import docx
import pandas as pd

# Handle relative import gracefully
try:
    from .llm_router import create_router_from_env
    from .collection_model import Collection
    from .database_manager import DatabaseManager
    from .common import get_env_var
    from .custom_embedding import CustomEmbedding
except ImportError:
    # Fallback for when module is imported directly
    try:
        from llm_router import create_router_from_env
        from collection_model import Collection
        from database_manager import DatabaseManager
        from common import get_env_var
        from custom_embedding import CustomEmbedding
    except ImportError:
        # Create a mock function if llm_router is not available
        def create_router_from_env():
            return None
        # Create mock classes
        class Collection:
            def exists(self, name): return True
        class DatabaseManager:
            pass
        # Import get_env_var from libs.common
        try:
            from libs.common import get_env_var
        except ImportError:
            # Fallback implementation if common module is not available
            def get_env_var(key, default=None):
                return os.environ.get(key, default)
        # Import CustomEmbedding from libs.custom_embedding
        try:
            from libs.custom_embedding import CustomEmbedding
        except ImportError:
            # Create a mock class if CustomEmbedding is not available
            class CustomEmbedding:
                def __init__(self, model, api_base, api_key):
                    pass
                def embed_query(self, text):
                    return [0.0] * 384
                def embed_documents(self, texts):
                    return [[0.0] * 384 for _ in texts]

# Load environment variables
load_dotenv(dotenv_path='.env', override=True)

# CustomEmbeddings class has been moved to libs/CustomEmbeddings.py

class TypesenseVectorDB:
    """
    Thư viện để sử dụng Typesense làm vector database cho việc embedding văn bản
    """
    
    def __init__(self, collection_name: str = "documents"):
        """
        Khởi tạo kết nối Typesense
        
        Args:
            collection_name (str): Tên collection trong Typesense
        """
        self.collection_name = collection_name
        
        # Khởi tạo Typesense client từ .env
        typesense_host = get_env_var('TYPESENSE_HOST', 'localhost')
        typesense_port = get_env_var('TYPESENSE_PORT', '8108')
        typesense_protocol = get_env_var('TYPESENSE_PROTOCOL', 'http')
        typesense_api_key = get_env_var('TYPESENSE_API_KEY', 'xyz')
        typesense_timeout = int(get_env_var('TYPESENSE_TIMEOUT', '60'))

        print(f"🔗 Kết nối Typesense: {typesense_protocol}://{typesense_host}:{typesense_port}")

        self.client = typesense.Client({
            'nodes': [{
                'host': typesense_host,
                'port': typesense_port,
                'protocol': typesense_protocol
            }],
            'api_key': typesense_api_key,
            'connection_timeout_seconds': typesense_timeout
        })
        
        # Khởi tạo embeddings
        embeddings_model = get_env_var('EMBEDDINGS_MODEL', 'text-embedding-ada-002')
        embeddings_api_base = get_env_var('EMBEDDINGS_API_BASE')
        embeddings_api_key = get_env_var('EMBEDDINGS_API_KEY')

        print(f"🔗 Embeddings: {embeddings_model} via {embeddings_api_base}")

        # Use custom embeddings class instead of OpenAI
        self.embeddings = CustomEmbedding(
            model=embeddings_model,
            api_base=embeddings_api_base,
            api_key=embeddings_api_key
        )
        
        self.embedding_dim = get_env_var('EMBEDDINGS_DIMENSION')
        
        # Khởi tạo LLM router
        self.llm_router = create_router_from_env()
        
        # Tạo collection nếu chưa tồn tại
        self._create_collection_if_not_exists()
    
    def _create_collection_if_not_exists(self):
        """Tạo collection nếu chưa tồn tại"""
        try:
            # Kiểm tra xem collection đã tồn tại chưa
            self.client.collections[self.collection_name].retrieve()
            print(f"✅ Collection '{self.collection_name}' đã tồn tại")
        except typesense.exceptions.ObjectNotFound:
            # Tạo collection mới
            schema = {
                'name': self.collection_name,
                'fields': [
                    {'name': 'id', 'type': 'string'},
                    {'name': 'content', 'type': 'string'},
                    {'name': 'title', 'type': 'string', 'optional': True},
                    {'name': 'source_file', 'type': 'string', 'optional': True},
                    {'name': 'chunk_index', 'type': 'int32', 'optional': True},
                    {'name': 'metadata_json', 'type': 'string', 'optional': True},  # Store as JSON string
                    {'name': 'embedding', 'type': 'float[]', 'num_dim': self.embedding_dim},  # all-MiniLM-L6-v2 dimension
                    {'name': 'created_at', 'type': 'int64'},
                    {'name': 'content_hash', 'type': 'string'}
                ]
            }
            
            self.client.collections.create(schema)
            print(f"✅ Đã tạo collection '{self.collection_name}'")
    
    def read_docx(self, file_path: str) -> str:
        """
        Đọc nội dung file Word
        
        Args:
            file_path (str): Đường dẫn đến file Word
            
        Returns:
            str: Nội dung văn bản
        """
        doc = docx.Document(file_path)
        full_text = []
        
        # Đọc các đoạn văn thông thường
        for para in doc.paragraphs:
            text = para.text.strip()
            if text:
                full_text.append(text)
        
        # Đọc nội dung trong bảng
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    if cell_text:
                        row_text.append(cell_text)
                if row_text:
                    full_text.append('\t'.join(row_text))
        
        return '\n'.join(full_text)
    
    def chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """
        Chia văn bản thành các chunks nhỏ hơn
        
        Args:
            text (str): Văn bản cần chia
            chunk_size (int): Kích thước mỗi chunk
            overlap (int): Số ký tự overlap giữa các chunks
            
        Returns:
            List[str]: Danh sách các chunks
        """
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            
            # Tìm điểm cắt tốt hơn (cuối câu hoặc đoạn)
            if end < len(text):
                last_period = chunk.rfind('.')
                last_newline = chunk.rfind('\n')
                cut_point = max(last_period, last_newline)
                
                if cut_point > start + chunk_size // 2:  # Chỉ cắt nếu không quá ngắn
                    chunk = text[start:start + cut_point + 1]
                    end = start + cut_point + 1
            
            chunks.append(chunk.strip())
            start = end - overlap
            
            if start >= len(text):
                break
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    def generate_content_hash(self, content: str) -> str:
        """Tạo hash cho nội dung để tránh duplicate"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def import_docx_to_typesense(self, file_path: str, title: str = None, metadata: Dict = None) -> Dict[str, Any]:
        """
        Import dữ liệu từ file docx vào Typesense
        
        Args:
            file_path (str): Đường dẫn đến file Word
            title (str): Tiêu đề tài liệu (optional)
            metadata (Dict): Metadata bổ sung (optional)
            
        Returns:
            Dict[str, Any]: Kết quả import
        """
        try:
            print(f"📖 Đang đọc file: {file_path}")
            
            # Đọc nội dung file
            content = self.read_docx(file_path)
            
            if not content.strip():
                return {
                    "success": False,
                    "error": "File không có nội dung hoặc không đọc được"
                }
            
            print(f"📄 Đã đọc {len(content)} ký tự")
            
            # Chia thành chunks
            chunks = self.chunk_text(content)
            print(f"🔪 Đã chia thành {len(chunks)} chunks")
            
            # Tạo embeddings và lưu vào Typesense
            documents = []
            successful_imports = 0
            
            for i, chunk in enumerate(chunks):
                try:
                    # Kiểm tra duplicate bằng content hash
                    content_hash = self.generate_content_hash(chunk)
                    
                    # Tìm kiếm document với hash tương tự
                    search_result = self.client.collections[self.collection_name].documents.search({
                        'q': '*',
                        'filter_by': f'content_hash:={content_hash}',
                        'per_page': 1
                    })
                    
                    if search_result['found'] > 0:
                        print(f"⚠️  Chunk {i+1} đã tồn tại, bỏ qua")
                        continue
                    
                    # Tạo embedding
                    print(f"🔄 Đang tạo embedding cho chunk {i+1}/{len(chunks)}")
                    embedding = self.embeddings.embed_query(chunk)
                    
                    # Tạo document
                    doc = {
                        'id': str(uuid.uuid4()),
                        'content': chunk,
                        'title': title or os.path.basename(file_path),
                        'source_file': os.path.basename(file_path),
                        'chunk_index': i,
                        'metadata_json': json.dumps(metadata or {}, ensure_ascii=False),
                        'embedding': embedding,
                        'created_at': int(datetime.now().timestamp()),
                        'content_hash': content_hash
                    }
                    
                    documents.append(doc)
                    
                except Exception as e:
                    print(f"❌ Lỗi xử lý chunk {i+1}: {e}")
                    continue
            
            # Bulk import vào Typesense
            if documents:
                print(f"💾 Đang import {len(documents)} documents vào Typesense...")
                
                # Import từng batch để tránh timeout
                batch_size = 10
                for i in range(0, len(documents), batch_size):
                    batch = documents[i:i + batch_size]
                    try:
                        result = self.client.collections[self.collection_name].documents.import_(batch)
                        successful_imports += len([r for r in result if r.get('success', False)])
                        print(f"✅ Đã import batch {i//batch_size + 1}: {len(batch)} documents")
                    except Exception as e:
                        print(f"❌ Lỗi import batch {i//batch_size + 1}: {e}")
            
            return {
                "success": True,
                "total_chunks": len(chunks),
                "imported_documents": successful_imports,
                "skipped_duplicates": len(chunks) - len(documents),
                "file_path": file_path,
                "title": title or os.path.basename(file_path)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi import file: {str(e)}"
            }

    def search_similar_documents(self, query: str, limit: int = 10, threshold: float = 0.7, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Tìm kiếm documents tương tự dựa trên vector similarity sử dụng embedding API

        Args:
            query (str): Câu hỏi/truy vấn
            limit (int): Số lượng kết quả tối đa
            threshold (float): Ngưỡng similarity (0-1)
            collection_name (Optional[str]): Tên collection để tìm kiếm (nếu None sẽ dùng collection mặc định)

        Returns:
            Dict[str, Any]: Kết quả tìm kiếm
        """
        try:
            # Sử dụng collection_name được truyền vào hoặc collection mặc định
            target_collection = collection_name or self.collection_name
            print(f"🔍 Đang tìm kiếm: '{query}' trong collection '{target_collection}' (sử dụng vector search)")

            # Tạo embedding cho query sử dụng embedding API
            try:
                print(f"🔄 Đang tạo embedding cho query...")
                query_embedding = self.embeddings.embed_query(query)
                print(f"✅ Đã tạo embedding thành công (dimension: {len(query_embedding)})")
                print(f"🎯 Embedding (first 5 values): {query_embedding[:5]}")
            except Exception as e:
                print(f"❌ Lỗi tạo embedding: {e}")
                print(f"🔄 Fallback sang text search...")

                # Fallback to text search if embedding fails
                search_result = self.client.collections[target_collection].documents.search({
                    'q': query,
                    'query_by': 'content',
                    'per_page': limit,
                    'sort_by': '_text_match:desc'
                })

                # Xử lý kết quả text search
                documents = []
                for hit in search_result.get('hits', []):
                    doc = hit['document']
                    text_match_info = hit.get('text_match_info', {})
                    score = text_match_info.get('score', 0) if text_match_info else 0
                    if isinstance(score, (int, float)) and score > 0:
                        similarity = min(1.0, score / 100.0)
                    else:
                        similarity = 0.5

                    # Parse metadata từ JSON string
                    try:
                        metadata = json.loads(doc.get('metadata_json', '{}'))
                    except:
                        metadata = {}

                    documents.append({
                        'id': doc['id'],
                        'content': doc['content'],
                        'title': doc.get('title', ''),
                        'source_file': doc.get('source_file', ''),
                        'chunk_index': doc.get('chunk_index', 0),
                        'similarity': similarity,
                        'metadata': metadata
                    })

                print(f"✅ Tìm thấy {len(documents)} documents phù hợp (text search fallback)")

                return {
                    "success": True,
                    "query": query,
                    "total_found": len(documents),
                    "documents": documents,
                    "threshold": threshold,
                    "search_method": "text_search_fallback"
                }

            # Sử dụng vector search với embedding
            # Sử dụng multi_search endpoint để tránh giới hạn query string length
            search_params = {
                'searches': [{
                    'collection': target_collection,
                    'q': query,
                    #'vector_query': f'embedding:([{",".join(map(str, query_embedding))}], k:{limit})',
                    'query_by': 'content',
                    'per_page': limit
                }]
            }

            print(search_params)
            try:
                # Thử sử dụng multi_search trước
                multi_search_result = self.client.multi_search.perform(search_params, {})
                search_result = multi_search_result['results'][0]
            except Exception as multi_search_error:
                print(f"⚠️  Multi-search failed: {multi_search_error}")
                print(f"🔄 Fallback sang text search do lỗi vector query...")

                # Fallback to text search if vector query is too long
                search_result = self.client.collections[target_collection].documents.search({
                    'q': query,
                    'query_by': 'content',
                    'per_page': limit,
                    'sort_by': '_text_match:desc'
                })

                # Xử lý kết quả text search fallback
                documents = []
                for hit in search_result.get('hits', []):
                    doc = hit['document']
                    text_match_info = hit.get('text_match_info', {})
                    score = text_match_info.get('score', 0) if text_match_info else 0
                    if isinstance(score, (int, float)) and score > 0:
                        similarity = min(1.0, score / 100.0)
                    else:
                        similarity = 0.5

                    # Parse metadata từ JSON string
                    try:
                        metadata = json.loads(doc.get('metadata_json', '{}'))
                    except:
                        metadata = {}

                    documents.append({
                        'id': doc['id'],
                        'content': doc['content'],
                        'title': doc.get('title', ''),
                        'source_file': doc.get('source_file', ''),
                        'chunk_index': doc.get('chunk_index', 0),
                        'similarity': similarity,
                        'metadata': metadata
                    })

                print(f"✅ Tìm thấy {len(documents)} documents phù hợp (text search fallback)")

                return {
                    "success": True,
                    "query": query,
                    "total_found": len(documents),
                    "documents": documents,
                    "threshold": threshold,
                    "search_method": "text_search_vector_fallback"
                }

            # Xử lý kết quả vector search
            documents = []
            for hit in search_result.get('hits', []):
                doc = hit['document']

                # Tính cosine similarity từ vector distance
                # Typesense trả về distance, cần convert sang similarity
                vector_distance = hit.get('vector_distance', 1.0)
                # Convert distance to similarity (distance càng nhỏ thì similarity càng cao)
                similarity = max(0.0, 1.0 - vector_distance)

                # Áp dụng threshold
                if similarity < threshold:
                    continue

                # Parse metadata từ JSON string
                try:
                    metadata = json.loads(doc.get('metadata_json', '{}'))
                except:
                    metadata = {}

                documents.append({
                    'id': doc['id'],
                    'content': doc['content'],
                    'title': doc.get('title', ''),
                    'source_file': doc.get('source_file', ''),
                    'chunk_index': doc.get('chunk_index', 0),
                    'similarity': similarity,
                    'metadata': metadata
                })

            # Sắp xếp theo similarity giảm dần
            documents.sort(key=lambda x: x['similarity'], reverse=True)

            print(f"✅ Tìm thấy {len(documents)} documents phù hợp (vector search, threshold: {threshold})")

            for doc in documents:
                print(f"   - {doc['content']} (similarity: {doc['similarity']:.3f})")
                print("______________________________________________________")
            
            #exit()
            
            return {
                "success": True,
                "query": query,
                "total_found": len(documents),
                "documents": documents,
                "threshold": threshold,
                "search_method": "vector_search"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi tìm kiếm: {str(e)}"
            }

    def search_and_answer(self, question: str, limit: int = 5, threshold: float = 0.7, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Tìm kiếm documents liên quan và sử dụng LLM để trả lời câu hỏi

        Args:
            question (str): Câu hỏi
            limit (int): Số lượng documents tối đa để tham khảo
            threshold (float): Ngưỡng similarity
            collection_name (Optional[str]): Tên collection để tìm kiếm (nếu None sẽ dùng collection mặc định)

        Returns:
            Dict[str, Any]: Câu trả lời và thông tin liên quan
        """
        try:
            target_collection = collection_name or self.collection_name
            print(f"❓ Đang trả lời câu hỏi: '{question}' từ collection '{target_collection}'")

            # Tìm kiếm documents liên quan
            search_result = self.search_similar_documents(question, limit, threshold, collection_name)

            if not search_result["success"]:
                return search_result

            documents = search_result["documents"]

            if not documents:
                return {
                    "success": True,
                    "question": question,
                    "answer": "Không tìm thấy thông tin liên quan trong cơ sở dữ liệu.",
                    "sources": [],
                    "confidence": 0.0
                }

            # Tạo context từ các documents tìm được
            context_parts = []
            sources = []

            for i, doc in enumerate(documents):
                context_parts.append(f"[Nguồn {i+1}] {doc['content']}")

                # Tạo source info với metadata
                source_info = {
                    'source_file': doc['source_file'],
                    'title': doc['title'],
                    'similarity': doc['similarity'],
                    'chunk_index': doc['chunk_index']
                }

                # Thêm metadata nếu có
                if 'metadata' in doc:
                    source_info['metadata'] = doc['metadata']
                elif 'metadata_json' in doc:
                    try:
                        source_info['metadata'] = json.loads(doc['metadata_json'])
                    except:
                        source_info['metadata'] = {}

                sources.append(source_info)

            context = "\n\n".join(context_parts)

            # Tạo prompt cho LLM
            prompt = f"""Dựa trên thông tin sau đây, hãy trả lời câu hỏi một cách chính xác và chi tiết:
THÔNG TIN THAM KHẢO:
{context}
CÂU HỎI: {question}
Hãy trả lời dựa trên thông tin được cung cấp.Trả lời đầy đủ nội dung ở phần Đáp án gồm cả các Lưu ý. Không giải thích gì thêm nếu ko có dữ liệu.Không trích dẫn nguồn."""

            # print(prompt)
            # exit()
                        # Sử dụng LLM router để trả lời
            llm_result = self.llm_router.make_request(prompt)

            if llm_result and llm_result.get('success', False):
                answer = llm_result['response']+".\nNếu quý khách có nhu cầu tư vấn thêm, vui lòng liên hệ Hotline 19000101."
                confidence = sum(doc['similarity'] for doc in documents) / len(documents)

                return {
                    "success": True,
                    "question": question,
                    "answer": answer,
                    "sources": sources,
                    "confidence": confidence,
                    "llm_used": llm_result.get('llm_name', 'Unknown'),
                    "total_documents_found": len(documents)
                }
            else:
                return {
                    "success": False,
                    "error": f"Lỗi từ LLM: {llm_result.get('error', 'Unknown error') if llm_result else 'No response'}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi trả lời câu hỏi: {str(e)}"
            }

    def delete_documents_by_file(self, source_file: str) -> Dict[str, Any]:
        """
        Xóa tất cả documents từ một file cụ thể

        Args:
            source_file (str): Tên file nguồn

        Returns:
            Dict[str, Any]: Kết quả xóa
        """
        try:
            # Tìm tất cả documents từ file này
            search_result = self.client.collections[self.collection_name].documents.search({
                'q': '*',
                'filter_by': f'source_file:={source_file}',
                'per_page': 250  # Giới hạn tối đa của Typesense
            })

            if search_result['found'] == 0:
                return {
                    "success": True,
                    "message": f"Không tìm thấy documents từ file '{source_file}'",
                    "deleted_count": 0
                }

            # Xóa từng document
            deleted_count = 0
            for hit in search_result['hits']:
                doc_id = hit['document']['id']
                try:
                    self.client.collections[self.collection_name].documents[doc_id].delete()
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ Lỗi xóa document {doc_id}: {e}")

            return {
                "success": True,
                "message": f"Đã xóa {deleted_count} documents từ file '{source_file}'",
                "deleted_count": deleted_count
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi xóa documents: {str(e)}"
            }

    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê về collection

        Returns:
            Dict[str, Any]: Thông tin thống kê
        """
        try:
            # Lấy thông tin collection
            collection_info = self.client.collections[self.collection_name].retrieve()

            # Đếm tổng số documents
            search_result = self.client.collections[self.collection_name].documents.search({
                'q': '*',
                'per_page': 0  # Chỉ lấy count
            })

            total_documents = search_result['found']

            # Thống kê theo source file
            files_stats = {}
            if total_documents > 0:
                # Lấy tất cả documents để thống kê
                all_docs = self.client.collections[self.collection_name].documents.search({
                    'q': '*',
                    'per_page': total_documents
                })

                for hit in all_docs['hits']:
                    doc = hit['document']
                    source_file = doc.get('source_file', 'Unknown')
                    if source_file not in files_stats:
                        files_stats[source_file] = 0
                    files_stats[source_file] += 1

            return {
                "success": True,
                "collection_name": self.collection_name,
                "total_documents": total_documents,
                "files_stats": files_stats,
                "schema": collection_info
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi lấy thống kê: {str(e)}"
            }

    def import_excel_to_typesense(self, file_path: str, title: str = None, metadata: Dict = None, collection_name: str = None) -> Dict[str, Any]:
        """
        Import dữ liệu từ file Excel vào Typesense
        Hỗ trợ cấu trúc: Chức năng, Câu hỏi, Đáp án

        Args:
            file_path (str): Đường dẫn đến file Excel
            title (str): Tiêu đề tài liệu (optional)
            metadata (Dict): Metadata bổ sung (optional)
            collection_name (str): Tên collection để sử dụng trong TypeSense (optional)

        Returns:
            Dict[str, Any]: Kết quả import
        """
        try:
            print(f"📊 Đang đọc file Excel: {file_path}")

            # Xác định collection sẽ sử dụng
            target_collection = collection_name or self.collection_name
            print(f"🎯 Sử dụng collection: '{target_collection}'")

            # Kiểm tra collection_name nếu được cung cấp
            if collection_name:
                print(f"🔍 Kiểm tra collection '{collection_name}' trong MySQL...")
                try:
                    collection_model = Collection()
                    if not collection_model.exists(collection_name):
                        return {
                            "success": False,
                            "error": f"Collection '{collection_name}' không tồn tại trong cơ sở dữ liệu MySQL. Vui lòng tạo collection trước khi import."
                        }
                    print(f"✅ Collection '{collection_name}' đã được xác nhận trong MySQL")
                except Exception as e:
                    print(f"⚠️ Không thể kiểm tra collection trong MySQL: {e}")
                    print("ℹ️ Tiếp tục import mà không kiểm tra collection...")

            # Tạo collection trong TypeSense nếu chưa tồn tại (cho cả collection mới và mặc định)
            print(f"🔍 Kiểm tra collection '{target_collection}' trong TypeSense...")
            collection_created = False
            try:
                # Kiểm tra xem collection đã tồn tại chưa
                self.client.collections[target_collection].retrieve()
                print(f"✅ Collection '{target_collection}' đã tồn tại trong TypeSense")
            except typesense.exceptions.ObjectNotFound:
                # Tạo collection mới trong TypeSense
                print(f"🔧 Tạo collection mới '{target_collection}' trong TypeSense...")
                schema = {
                    'name': target_collection,
                    'fields': [
                        {'name': 'id', 'type': 'string'},
                        {'name': 'content', 'type': 'string'},
                        {'name': 'title', 'type': 'string', 'optional': True},
                        {'name': 'source_file', 'type': 'string', 'optional': True},
                        {'name': 'chunk_index', 'type': 'int32', 'optional': True},
                        {'name': 'metadata_json', 'type': 'string', 'optional': True},  # Store as JSON string
                        {'name': 'embedding', 'type': 'float[]', 'num_dim': self.embedding_dim},  # all-MiniLM-L6-v2 dimension
                        {'name': 'created_at', 'type': 'int64'},
                        {'name': 'content_hash', 'type': 'string'}
                    ]
                }

                self.client.collections.create(schema)
                collection_created = True
                print(f"✅ Đã tạo collection '{target_collection}' trong TypeSense")

            # Đọc file Excel
            try:
                df = pd.read_excel(file_path)
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Không thể đọc file Excel: {str(e)}"
                }

            # Kiểm tra cấu trúc file Excel - hỗ trợ nhiều biến thể tên cột
            column_mapping = {}

            # Định nghĩa các biến thể tên cột có thể có
            function_variants = ['Chức năng', 'CHỨC NĂNG', 'Tính năng', 'TÍNH NĂNG', 'Function', 'FUNCTION']
            question_variants = ['Câu hỏi', 'CÂU HỎI', 'Cau hoi', 'CAU HOI', 'Question', 'QUESTION']
            answer_variants = ['Đáp án', 'ĐÁP ÁN', 'Dap an', 'DAP AN', 'Trả lời', 'TRẢ LỜI', 'Tra loi', 'TRA LOI', 'Answer', 'ANSWER']

            # Tìm cột chức năng
            function_col = None
            for variant in function_variants:
                if variant in df.columns:
                    function_col = variant
                    column_mapping['function'] = variant
                    break

            # Tìm cột câu hỏi
            question_col = None
            for variant in question_variants:
                if variant in df.columns:
                    question_col = variant
                    column_mapping['question'] = variant
                    break

            # Tìm cột đáp án
            answer_col = None
            for variant in answer_variants:
                if variant in df.columns:
                    answer_col = variant
                    column_mapping['answer'] = variant
                    break

            # Kiểm tra các cột bắt buộc
            missing_columns = []
            if not question_col:
                missing_columns.append("Câu hỏi (hoặc các biến thể: CÂU HỎI, Question, etc.)")
            if not answer_col:
                missing_columns.append("Đáp án (hoặc các biến thể: TRẢ LỜI, Answer, etc.)")

            if missing_columns:
                available_columns = list(df.columns)
                return {
                    "success": False,
                    "error": f"File Excel thiếu các cột bắt buộc: {missing_columns}. Các cột hiện có: {available_columns}"
                }

            print(f"✅ Đã nhận diện cấu trúc cột:")
            print(f"   - Chức năng: {function_col or 'Không có'}")
            print(f"   - Câu hỏi: {question_col}")
            print(f"   - Đáp án: {answer_col}")

            print(f"📄 Đã đọc {len(df)} dòng dữ liệu")

            # Loại bỏ các dòng trống
            df = df.dropna(subset=[question_col, answer_col])
            print(f"📄 Sau khi loại bỏ dòng trống: {len(df)} dòng")

            if len(df) == 0:
                return {
                    "success": False,
                    "error": "Không có dữ liệu hợp lệ trong file Excel"
                }

            # Tạo hash cho file để tránh trùng lặp
            file_content_hash = hashlib.md5(str(df.values.tolist()).encode()).hexdigest()

            # Chuẩn bị documents để import
            documents = []
            successful_imports = 0

            for index, row in df.iterrows():
                try:
                    chuc_nang = str(row[function_col]).strip() if function_col and pd.notna(row[function_col]) else ""
                    cau_hoi = str(row[question_col]).strip()
                    dap_an = str(row[answer_col]).strip()

                    # Debug: In ra từng trường riêng biệt
                    print(f"🔍 Dòng {index+1}:")
                    print(f"   - Chức năng: '{chuc_nang}'")
                    print(f"   - Câu hỏi: '{cau_hoi}'")
                    print(f"   - Đáp án: '{dap_an}'")

                    # Tạo nội dung kết hợp để embedding
                    content = f"Chức năng: {chuc_nang}\nCâu hỏi: {cau_hoi}\nĐáp án: {dap_an}"
                    print(f"📝 Content tổng hợp:\n{content}")

                    # Tạo hash cho document để tránh trùng lặp
                    content_hash = hashlib.md5(content.encode()).hexdigest()
                    print(f"🔐 Content hash: {content_hash}")

                    # Kiểm tra trùng lặp
                    existing_docs = self.client.collections[target_collection].documents.search({
                        'q': '*',
                        'filter_by': f'content_hash:={content_hash}',
                        'per_page': 1
                    })

                    if existing_docs['found'] > 0:
                        print(f"⏭️  Bỏ qua dòng {index+1} (đã tồn tại)")
                        continue

                    # Tạo embedding
                    print(f"🔄 Đang tạo embedding cho dòng {index+1}/{len(df)}")
                    embedding = self.embeddings.embed_query(content)
                    print(f"🎯 Embedding (first 5 values): {embedding[:5]}")
                    print(f"🎯 Embedding length: {len(embedding)}")
                    print("_____________________________________________________")
                    # Tạo metadata mở rộng
                    extended_metadata = {
                        "chuc_nang": chuc_nang,
                        "cau_hoi": cau_hoi,
                        "dap_an": dap_an,
                        "row_index": index + 1,
                        "data_type": "qa_pair",
                        **(metadata or {})
                    }

                    # Tạo document
                    doc = {
                        'id': str(uuid.uuid4()),
                        'content': content,
                        'title': title or f"QA - {chuc_nang}" if chuc_nang else f"QA - Row {index+1}",
                        'source_file': os.path.basename(file_path),
                        'chunk_index': index,
                        'metadata_json': json.dumps(extended_metadata, ensure_ascii=False),
                        'embedding': embedding,
                        'created_at': int(datetime.now().timestamp()),
                        'content_hash': content_hash
                    }

                    documents.append(doc)

                except Exception as e:
                    print(f"❌ Lỗi xử lý dòng {index+1}: {e}")
                    continue

            # Bulk import vào Typesense
            if documents:
                print(f"💾 Đang import {len(documents)} documents vào collection '{target_collection}'...")

                # Import từng batch để tránh timeout
                batch_size = 10
                for i in range(0, len(documents), batch_size):
                    batch = documents[i:i + batch_size]
                    try:
                        result = self.client.collections[target_collection].documents.import_(batch)
                        successful_imports += len([r for r in result if r.get('success', False)])
                        print(f"✅ Đã import batch {i//batch_size + 1}: {len(batch)} documents")
                    except Exception as e:
                        print(f"❌ Lỗi import batch {i//batch_size + 1}: {e}")

            return {
                "success": True,
                "total_rows": len(df),
                "imported_documents": successful_imports,
                "skipped_duplicates": len(df) - len(documents),
                "file_path": file_path,
                "title": title or os.path.basename(file_path),
                "collection_name": target_collection,
                "collection_verified": collection_name is not None,
                "collection_created": collection_created  # Indicates if we created a new collection in TypeSense
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi import file Excel: {str(e)}"
            }
